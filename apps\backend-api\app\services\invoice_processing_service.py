"""
Invoice Processing Service - Hanterar fakturabehandlingsflödet
"""

import logging
import time
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from uuid import UUID

from app.models import Invoice, Session as ProcessingSession, SessionLog
from app.services.llm_provider import get_llm_service, LLMResponse, reset_llm_service
from app.services.prompt_service import get_prompt_service
from app.services.ocr_service import OCRService
from app.services.vector_service import VectorService

logger = logging.getLogger(__name__)


class InvoiceProcessingService:
    """Service för att hantera fakturabehandlingsflödet"""
    
    def __init__(self, db: Session):
        self.db = db
        # Återställ LLM service för att säkerställa att den läser senaste konfigurationen
        reset_llm_service()
        self.llm_service = get_llm_service()
        self.prompt_service = get_prompt_service()
        self.ocr_service = OCRService()
        self.vector_service = VectorService()
    
    async def process_invoice(self, invoice_id: UUID, tenant_id: UUID) -> Dict[str, Any]:
        """<PERSON>ör hela fakturabehandlingsflödet"""
        try:
            # Hämta faktura
            invoice = self.db.query(Invoice).filter(
                Invoice.id == invoice_id,
                Invoice.tenant_id == tenant_id
            ).first()
            
            if not invoice:
                raise ValueError(f"Invoice {invoice_id} not found")
            
            # Skapa eller hämta session
            session = self._get_or_create_session(invoice)
            
            # Kör flödet steg för steg
            result = await self._run_processing_flow(session, invoice)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing invoice {invoice_id}: {e}")
            raise
    
    def _get_or_create_session(self, invoice: Invoice) -> ProcessingSession:
        """Skapa eller hämta session för fakturan"""
        session = self.db.query(ProcessingSession).filter(
            ProcessingSession.invoice_id == invoice.id
        ).first()
        
        if not session:
            session = ProcessingSession(
                tenant_id=invoice.tenant_id,
                invoice_id=invoice.id,
                status="pending"
            )
            self.db.add(session)
            self.db.commit()
            self.db.refresh(session)
            logger.info(f"Created new session {session.id} for invoice {invoice.id}")
        
        return session
    
    async def _run_processing_flow(self, session: ProcessingSession, invoice: Invoice) -> Dict[str, Any]:
        """Kör hela bearbetningsflödet"""
        current_step = None
        try:
            session.status = "processing"
            self.db.commit()

            # Steg 1: Extrahera
            await self._step_extrahera(session, invoice)

            # Steg 2: Kontext
            await self._step_kontext(session)

            # Steg 3: Hitta konto
            await self._step_hitta_konto(session)

            # Steg 4: Bokföra
            await self._step_bokfora(session, invoice)

            # Markera som klar
            session.status = "completed"
            session.current_step = None
            invoice.status = "completed"
            self.db.commit()

            return {
                "session_id": str(session.id),
                "status": "completed",
                "message": "Invoice processing completed successfully"
            }

        except Exception as e:
            # Capture current step before any database operations that might fail
            try:
                current_step = session.current_step
            except Exception:
                current_step = "unknown"

            # Handle database session rollback state properly
            await self._handle_processing_error(session, invoice, current_step, e)

            logger.error(f"Processing failed for session {session.id}: {e}")
            raise

    async def _handle_processing_error(self, session: ProcessingSession, invoice: Invoice, current_step: str, error: Exception):
        """Handle processing errors with proper database session management"""
        from app.database import recover_database_session, safe_commit

        try:
            # First, try to recover the database session
            recover_database_session(self.db)

            # Refresh objects from database to ensure we have clean state
            self.db.refresh(session)
            self.db.refresh(invoice)

            # Update status in a new transaction
            session.status = "failed"
            session.error_message = str(error)
            session.failed_step = current_step
            invoice.status = "failed"
            invoice.processing_error = str(error)

            # Use safe commit to handle any remaining transaction issues
            if safe_commit(self.db, f"error status update for session {session.id}"):
                logger.info(f"Successfully updated session {session.id} status to failed")
            else:
                logger.error(f"Failed to commit error status update for session {session.id}")

        except Exception as commit_error:
            logger.error(f"Failed to update session status after error: {commit_error}")
            # If we can't update the status, at least ensure the session is rolled back
            try:
                self.db.rollback()
            except Exception:
                pass
    
    async def _step_extrahera(self, session: ProcessingSession, invoice: Invoice):
        """Steg 1: Extrahera fakturainformation"""
        session.current_step = "extrahera"
        self.db.commit()

        start_time = time.time()

        # Debug: Logga vilken modell som används
        provider_info = self.llm_service.get_provider_info()
        logger.info(f"Using LLM provider: {provider_info}")

        try:
            # Bearbeta filen baserat på typ
            text_content, image_data = self.ocr_service.process_file_for_llm(
                invoice.file_data,
                invoice.file_type
            )

            # Formatera prompt baserat på filtyp
            if text_content:
                # PDF med extraherad text
                system_prompt, user_prompt = self.prompt_service.format_prompt(
                    "extrahera",
                    file_data=text_content
                )
                # Skicka till LLM utan bild
                response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            else:
                # Bild - skicka som base64 (kan vara original bild eller konverterad PDF)
                system_prompt, user_prompt = self.prompt_service.format_prompt(
                    "extrahera",
                    file_data="Se bifogad bild för fakturainnehåll"
                )
                # Bestäm rätt filtyp för LLM
                # Om original var PDF som konverterades till bild, använd 'jpeg'
                # Annars använd original filtyp
                llm_file_type = 'jpeg' if invoice.file_type.lower() == 'pdf' else invoice.file_type

                # Skicka till LLM med bild
                response = await self.llm_service.send_prompt(
                    user_prompt,
                    system_prompt,
                    image_data=image_data,
                    file_type=llm_file_type
                )
            
            if not response.success:
                raise Exception(f"LLM error in extrahera step: {response.error}")
            
            # Spara resultat
            session.extracted_data = response.data
            session.extracted_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="extrahera",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )
            
            self.db.commit()
            logger.info(f"Completed extrahera step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="extrahera",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            raise
    
    async def _step_kontext(self, session: ProcessingSession):
        """Steg 2: Skapa kontext"""
        session.current_step = "kontext"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Formatera prompt
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "kontext",
                extracted_data=session.extracted_data
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in kontext step: {response.error}")
            
            # Spara resultat
            session.context_data = response.data
            session.context_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="kontext",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )
            
            self.db.commit()
            logger.info(f"Completed kontext step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="kontext",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            raise
    
    async def _step_hitta_konto(self, session: ProcessingSession):
        """Steg 3: Hitta lämpliga konton med RAG"""
        session.current_step = "hitta_konto"
        self.db.commit()

        start_time = time.time()
        user_prompt = ""

        try:
            # Hämta RAG-kontext från liknande fakturor och tidigare kontobeslut
            # Använd try-catch för att hantera eventuella databasfel
            try:
                rag_context = await self._get_account_rag_context(session)
            except Exception as rag_error:
                logger.warning(f"Failed to get RAG context for session {session.id}: {rag_error}")
                # Fortsätt utan RAG-kontext istället för att krascha
                rag_context = "Ingen RAG-kontext tillgänglig på grund av databasfel."

            # Formatera prompt med RAG-kontext
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "hitta_konto",
                context_data=session.context_data,
                rag_context=rag_context
            )

            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)

            if not response.success:
                raise Exception(f"LLM error in hitta_konto step: {response.error}")

            # Försök att parsa JSON data
            try:
                import json
                account_data = json.loads(response.data) if isinstance(response.data, str) else response.data
            except json.JSONDecodeError:
                # Om det inte är JSON, spara som text
                account_data = {"raw_response": response.data}

            # Spara resultat
            session.account_data = account_data
            session.account_reasoning = response.reasoning

            # Logga steget
            self._log_step(
                session=session,
                step_name="hitta_konto",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )

            self.db.commit()
            logger.info(f"Completed hitta_konto step for session {session.id}")

        except Exception as e:
            # Ensure we rollback any pending transaction before logging
            try:
                self.db.rollback()
            except Exception:
                pass

            self._log_step(
                session=session,
                step_name="hitta_konto",
                prompt_sent=user_prompt,
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            raise

    async def _get_account_rag_context(self, session: ProcessingSession) -> str:
        """Hämta RAG-kontext för kontoval från liknande fakturor och tidigare kontobeslut"""
        rag_parts = []
        similar_contexts = []
        similar_accounting_entries = []

        try:
            # 1. Hämta kontext från liknande fakturor
            if session.context_data:
                try:
                    similar_contexts = await self.vector_service.get_similar_contexts(
                        db=self.db,
                        tenant_id=session.invoice.tenant_id,
                        query_content=session.context_data,
                        limit=3
                    )

                    if similar_contexts:
                        rag_parts.append("## Kontext från liknande fakturor:")
                        for i, context in enumerate(similar_contexts, 1):
                            rag_parts.append(f"### Liknande faktura {i}:")
                            rag_parts.append(context)
                            rag_parts.append("")
                except Exception as e:
                    logger.warning(f"Failed to get similar contexts: {e}")
                    # Continue without similar contexts

            # 2. Hämta tidigare kontobeslut för liknande leverantörer/kategorier
            try:
                similar_accounting_entries = await self._get_similar_accounting_entries(session)

                if similar_accounting_entries:
                    rag_parts.append("## Tidigare kontobeslut för liknande fakturor:")
                    for entry_group in similar_accounting_entries:
                        supplier = entry_group.get('supplier_name', 'Okänd leverantör')
                        rag_parts.append(f"### {supplier}:")

                        for entry in entry_group.get('entries', []):
                            amount_info = ""
                            if entry.get('debit_amount'):
                                amount_info = f" (Debet: {entry['debit_amount']} kr)"
                            elif entry.get('credit_amount'):
                                amount_info = f" (Kredit: {entry['credit_amount']} kr)"

                            confidence_info = f" [Konfidensgrad: {entry.get('confidence_score', 0):.2f}]"

                            rag_parts.append(
                                f"- Konto {entry['account_code']} - {entry['account_name']}"
                                f"{amount_info}{confidence_info}"
                            )

                            if entry.get('description'):
                                rag_parts.append(f"  Beskrivning: {entry['description']}")

                        rag_parts.append("")
            except Exception as e:
                logger.warning(f"Failed to get similar accounting entries: {e}")
                # Continue without accounting entries

            # 3. Sammanställ RAG-kontext
            if rag_parts:
                rag_context = "\n".join(rag_parts)
                logger.info(f"Generated RAG context with {len(similar_contexts)} similar contexts and {len(similar_accounting_entries)} accounting entry groups")
                return rag_context
            else:
                logger.info("No RAG context found for account selection")
                return "Ingen tidigare kontext eller kontobeslut hittades för liknande fakturor."

        except Exception as e:
            logger.error(f"Error generating RAG context for account selection: {e}")
            # Return a safe fallback instead of raising
            return "Fel vid hämtning av RAG-kontext för kontoval. Fortsätter utan historisk kontext."

    async def _get_similar_accounting_entries(self, session: ProcessingSession) -> List[Dict]:
        """Hämta tidigare kontobeslut för liknande leverantörer och kategorier"""
        try:
            from app.models.invoice import AccountingEntry

            # Hämta leverantörens namn från nuvarande faktura
            current_supplier = session.invoice.supplier_name

            # Sök efter tidigare kontobeslut för samma leverantör
            supplier_entries = []
            if current_supplier:
                try:
                    supplier_query = self.db.query(AccountingEntry).join(Invoice).filter(
                        Invoice.tenant_id == session.invoice.tenant_id,
                        Invoice.supplier_name.ilike(f"%{current_supplier}%"),
                        AccountingEntry.is_validated == True,  # Endast validerade poster
                        Invoice.id != session.invoice.id  # Exkludera nuvarande faktura
                    ).order_by(AccountingEntry.confidence_score.desc()).limit(10)

                    supplier_entries = supplier_query.all()
                except Exception as query_error:
                    logger.warning(f"Failed to query supplier entries for {current_supplier}: {query_error}")
                    # Continue with empty list

            # Gruppera efter leverantör
            grouped_entries = {}
            for entry in supplier_entries:
                supplier_name = entry.invoice.supplier_name
                if supplier_name not in grouped_entries:
                    grouped_entries[supplier_name] = {
                        'supplier_name': supplier_name,
                        'entries': []
                    }

                grouped_entries[supplier_name]['entries'].append({
                    'account_code': entry.account_code,
                    'account_name': entry.account_name,
                    'debit_amount': entry.debit_amount,
                    'credit_amount': entry.credit_amount,
                    'description': entry.description,
                    'confidence_score': entry.confidence_score
                })

            # Konvertera till lista och begränsa antal poster per leverantör
            result = []
            for supplier_data in grouped_entries.values():
                # Begränsa till max 5 poster per leverantör
                supplier_data['entries'] = supplier_data['entries'][:5]
                result.append(supplier_data)

            # Begränsa totalt antal leverantörer
            result = result[:3]

            logger.info(f"Found {len(result)} supplier groups with similar accounting entries")
            return result

        except Exception as e:
            logger.error(f"Error fetching similar accounting entries: {e}")
            return []
    
    async def _step_bokfora(self, session: ProcessingSession, invoice: Invoice):
        """Steg 4: Bokföra (förbereda för ERP integration)"""
        session.current_step = "bokfora"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Bestäm ERP system baserat på import_typ
            erp_system = "manual" if invoice.import_typ == "manuell" else invoice.import_typ
            
            # Formatera prompt
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "bokfora",
                context_data=session.context_data,
                account_data=session.account_data,
                erp_system=erp_system
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in bokfora step: {response.error}")
            
            # Försök att parsa JSON data
            try:
                import json
                booking_result = json.loads(response.data) if isinstance(response.data, str) else response.data
            except json.JSONDecodeError:
                booking_result = {"raw_response": response.data, "status": "needs_review"}
            
            # Spara resultat
            session.booking_result = booking_result
            session.booking_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="bokfora",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )
            
            self.db.commit()
            logger.info(f"Completed bokfora step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="bokfora",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            raise
    
    def _log_step(self, session: ProcessingSession, step_name: str, prompt_sent: str,
                  llm_response: str, reasoning: str, execution_time_ms: float,
                  success: bool, error_message: str = None, input_tokens: int = None,
                  output_tokens: int = None):
        """Logga ett bearbetningssteg"""
        try:
            import json

            # Konvertera llm_response till sträng om det är ett dict
            if isinstance(llm_response, dict):
                llm_response = json.dumps(llm_response, ensure_ascii=False)
            elif llm_response is None:
                llm_response = ""

            log_entry = SessionLog(
                tenant_id=session.tenant_id,
                session_id=session.id,
                step_name=step_name,
                prompt_sent=prompt_sent,
                llm_response=str(llm_response),
                reasoning=reasoning,
                execution_time_ms=execution_time_ms,
                success=success,
                error_message=error_message,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

            self.db.add(log_entry)

            # Uppdatera session token-summering
            if input_tokens is not None:
                session.total_input_tokens = (session.total_input_tokens or 0) + input_tokens
            if output_tokens is not None:
                session.total_output_tokens = (session.total_output_tokens or 0) + output_tokens

            # Commit sker i anropande metod

        except Exception as log_error:
            # Don't let logging errors break the main flow
            logger.error(f"Failed to log step {step_name} for session {session.id}: {log_error}")
            # Try to rollback any partial log entry
            try:
                self.db.rollback()
            except Exception:
                pass
